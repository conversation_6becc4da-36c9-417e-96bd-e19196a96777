server:
  port: 8080

spring:
  application:
    name: qr-ordering-system
  
  # PostgreSQL数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.postgresql.Driver
      url: ********************************************
      username: postgres
      password: 123456
      initial-size: 5
      min-idle: 5
      max-active: 20

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: AUTO