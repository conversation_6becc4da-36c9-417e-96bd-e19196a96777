server:
  port: 8080

spring:
  application:
    name: qr-ordering-system
  
  # H2内存数据库配置
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
      
  # 初始化数据
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
      data-locations: classpath:data.sql

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: AUTO

logging:
  level:
    com.qr.ordering: DEBUG