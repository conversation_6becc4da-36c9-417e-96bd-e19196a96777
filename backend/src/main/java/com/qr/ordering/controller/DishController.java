package com.qr.ordering.controller;

import com.qr.ordering.common.Result;
import com.qr.ordering.entity.Category;
import com.qr.ordering.entity.Dish;
import com.qr.ordering.service.CategoryService;
import com.qr.ordering.service.DishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/dish")
@Api(tags = "菜品管理")
@CrossOrigin
public class DishController {

    @Autowired
    private DishService dishService;

    @Autowired
    private CategoryService categoryService;

    @GetMapping("/categories")
    @ApiOperation("获取菜品分类")
    public Result<List<Category>> getCategories() {
        List<Category> categories = categoryService.list();
        return Result.success(categories);
    }

    @GetMapping("/list")
    @ApiOperation("根据分类获取菜品列表")
    public Result<List<Dish>> getDishList(@RequestParam(required = false) Long categoryId) {
        List<Dish> dishes = dishService.getDishList(categoryId);
        return Result.success(dishes);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取菜品详情")
    public Result<Dish> getDishDetail(@PathVariable Long id) {
        Dish dish = dishService.getById(id);
        return Result.success(dish);
    }
}