@echo off
chcp 65001 >nul
echo ====================================
echo 启动模拟后端服务
echo ====================================
echo.

echo 检查Node.js环境...
node -v >nul 2>&1
if %errorlevel% neq 0 (
    echo × Node.js未安装，请先安装Node.js
    pause
    exit /b 1
)

echo ✓ Node.js已安装
node -v

echo.
echo 检查依赖...
if not exist node_modules (
    echo 正在安装依赖...
    npm install
)

echo.
echo 正在启动模拟后端服务...
echo 服务将在 http://localhost:8080 运行
echo.
node server.js

pause