const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = 8080;

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 模拟数据
const categories = [
  { id: 1, name: '热菜', sort: 1, status: 1 },
  { id: 2, name: '凉菜', sort: 2, status: 1 },
  { id: 3, name: '汤品', sort: 3, status: 1 },
  { id: 4, name: '主食', sort: 4, status: 1 },
  { id: 5, name: '饮品', sort: 5, status: 1 }
];

const dishes = [
  { id: 1, name: '宫保鸡丁', categoryId: 1, price: 28.00, image: '', description: '经典川菜，鸡肉嫩滑，配菜丰富', status: 1, sort: 1 },
  { id: 2, name: '麻婆豆腐', categoryId: 1, price: 18.00, image: '', description: '麻辣鲜香，豆腐嫩滑', status: 1, sort: 2 },
  { id: 3, name: '糖醋里脊', categoryId: 1, price: 32.00, image: '', description: '酸甜可口，外酥内嫩', status: 1, sort: 3 },
  { id: 4, name: '红烧肉', categoryId: 1, price: 38.00, image: '', description: '肥而不腻，入口即化', status: 1, sort: 4 },
  { id: 5, name: '鱼香肉丝', categoryId: 1, price: 22.00, image: '', description: '川菜经典，酸甜微辣', status: 1, sort: 5 },
  
  { id: 6, name: '凉拌黄瓜', categoryId: 2, price: 12.00, image: '', description: '清爽解腻，爽脆可口', status: 1, sort: 1 },
  { id: 7, name: '拍黄瓜', categoryId: 2, price: 10.00, image: '', description: '简单美味，清香脆嫩', status: 1, sort: 2 },
  { id: 8, name: '凉拌木耳', categoryId: 2, price: 15.00, image: '', description: '爽脆可口，营养丰富', status: 1, sort: 3 },
  
  { id: 9, name: '紫菜蛋花汤', categoryId: 3, price: 15.00, image: '', description: '清淡营养，老少皆宜', status: 1, sort: 1 },
  { id: 10, name: '西红柿鸡蛋汤', categoryId: 3, price: 16.00, image: '', description: '酸甜开胃，营养丰富', status: 1, sort: 2 },
  { id: 11, name: '冬瓜排骨汤', categoryId: 3, price: 25.00, image: '', description: '清热解暑，鲜美可口', status: 1, sort: 3 },
  
  { id: 12, name: '米饭', categoryId: 4, price: 3.00, image: '', description: '香甜软糯', status: 1, sort: 1 },
  { id: 13, name: '面条', categoryId: 4, price: 8.00, image: '', description: '劲道爽滑', status: 1, sort: 2 },
  { id: 14, name: '炒饭', categoryId: 4, price: 15.00, image: '', description: '粒粒分明，香味扑鼻', status: 1, sort: 3 },
  
  { id: 15, name: '可乐', categoryId: 5, price: 6.00, image: '', description: '冰爽解腻', status: 1, sort: 1 },
  { id: 16, name: '果汁', categoryId: 5, price: 8.00, image: '', description: '新鲜果汁，营养健康', status: 1, sort: 2 },
  { id: 17, name: '茶水', categoryId: 5, price: 5.00, image: '', description: '清香解渴', status: 1, sort: 3 }
];

// 存储订单
let orders = [];
let orderIdCounter = 1;

// 统一响应格式
const success = (data = null, message = '操作成功') => ({
  code: 200,
  message,
  data
});

const error = (message = '操作失败', code = 500) => ({
  code,
  message,
  data: null
});

// API路由

// 获取菜品分类
app.get('/api/dish/categories', (req, res) => {
  console.log('获取菜品分类');
  res.json(success(categories));
});

// 获取菜品列表
app.get('/api/dish/list', (req, res) => {
  const { categoryId } = req.query;
  console.log('获取菜品列表, categoryId:', categoryId);
  
  let result = dishes;
  if (categoryId) {
    result = dishes.filter(dish => dish.categoryId == categoryId);
  }
  
  res.json(success(result));
});

// 获取菜品详情
app.get('/api/dish/:id', (req, res) => {
  const { id } = req.params;
  console.log('获取菜品详情, id:', id);
  
  const dish = dishes.find(d => d.id == id);
  if (dish) {
    res.json(success(dish));
  } else {
    res.json(error('菜品不存在'));
  }
});

// 创建订单
app.post('/api/order/create', (req, res) => {
  console.log('创建订单', req.body);
  
  const order = {
    id: orderIdCounter++,
    orderNumber: 'ORD' + Date.now(),
    tableNumber: req.body.tableNumber || '1',
    items: req.body.items || [],
    totalAmount: req.body.totalAmount || 0,
    status: 1, // 待支付
    createTime: new Date().toISOString(),
    remark: req.body.remark || ''
  };
  
  orders.push(order);
  res.json(success(order, '订单创建成功'));
});

// 获取订单列表
app.get('/api/order/list', (req, res) => {
  console.log('获取订单列表');
  
  // 返回最近的订单，按创建时间倒序
  const sortedOrders = [...orders].sort((a, b) => 
    new Date(b.createTime) - new Date(a.createTime)
  );
  
  res.json(success(sortedOrders));
});

// 获取订单详情
app.get('/api/order/:id', (req, res) => {
  const { id } = req.params;
  console.log('获取订单详情, id:', id);
  
  const order = orders.find(o => o.id == id);
  if (order) {
    res.json(success(order));
  } else {
    res.json(error('订单不存在'));
  }
});

// 支付订单（模拟）
app.post('/api/order/pay/:id', (req, res) => {
  const { id } = req.params;
  console.log('支付订单, id:', id);
  
  const order = orders.find(o => o.id == id);
  if (order) {
    order.status = 2; // 已支付
    order.paymentTime = new Date().toISOString();
    order.paymentMethod = req.body.paymentMethod || 1; // 1-微信 2-支付宝
    res.json(success(order, '支付成功'));
  } else {
    res.json(error('订单不存在'));
  }
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json(success({ status: 'running', time: new Date() }));
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`
====================================
模拟后端服务已启动！
====================================
服务地址: http://localhost:${PORT}
API文档:
  - GET  /api/dish/categories     获取菜品分类
  - GET  /api/dish/list           获取菜品列表
  - GET  /api/dish/:id            获取菜品详情
  - POST /api/order/create        创建订单
  - GET  /api/order/list          获取订单列表
  - GET  /api/order/:id           获取订单详情
  - POST /api/order/pay/:id       支付订单
====================================
  `);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  process.exit(0);
});