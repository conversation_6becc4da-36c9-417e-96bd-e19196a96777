{"name": "qr-ordering-frontend", "version": "1.0.0", "description": "扫码点餐系统客户端", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.4.0", "vant": "^4.6.0", "@vant/touch-emulator": "^1.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "@rushstack/eslint-patch": "^1.3.2", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "prettier": "^3.0.0", "vite": "^4.4.0", "vite-plugin-style-import": "^2.0.0"}}