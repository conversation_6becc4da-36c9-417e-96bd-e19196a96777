import axios from 'axios'
import { showToast } from 'vant'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加token等
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    if (data.code === 200) {
      return data.data
    } else {
      showToast(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  error => {
    showToast(error.message || '网络错误')
    return Promise.reject(error)
  }
)

// 菜品相关API
export const dishApi = {
  // 获取菜品分类
  getCategories() {
    return api.get('/dish/categories')
  },
  
  // 获取菜品列表
  getDishList(categoryId) {
    return api.get('/dish/list', { params: { categoryId } })
  },
  
  // 获取菜品详情
  getDishDetail(id) {
    return api.get(`/dish/${id}`)
  }
}

// 订单相关API
export const orderApi = {
  // 创建订单
  createOrder(orderData) {
    return api.post('/order/create', orderData)
  },
  
  // 获取订单列表
  getOrderList() {
    return api.get('/order/list')
  },
  
  // 获取订单详情
  getOrderDetail(id) {
    return api.get(`/order/${id}`)
  }
}

export default api