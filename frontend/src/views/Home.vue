<template>
  <div class="home">
    <!-- 导航栏 -->
    <van-nav-bar title="扫码点餐" />
    
    <!-- 轮播图 -->
    <van-swipe class="swipe" :autoplay="3000" indicator-color="white">
      <van-swipe-item class="swipe-item">
        <div class="banner-content">
          <h2>欢迎光临</h2>
          <p>精选美食，优质服务</p>
        </div>
      </van-swipe-item>
      <van-swipe-item class="swipe-item">
        <div class="banner-content">
          <h2>新鲜食材</h2>
          <p>每日新鲜采购，品质保证</p>
        </div>
      </van-swipe-item>
    </van-swipe>
    
    <!-- 功能菜单 -->
    <div class="menu-grid">
      <div class="menu-item" @click="goToMenu">
        <van-icon name="apps-o" size="30" />
        <span>点餐</span>
      </div>
      <div class="menu-item" @click="goToCart">
        <van-icon name="shopping-cart-o" size="30" />
        <span>购物车</span>
        <van-badge v-if="cartStore.totalCount > 0" :content="cartStore.totalCount" />
      </div>
      <div class="menu-item" @click="goToOrder">
        <van-icon name="records" size="30" />
        <span>我的订单</span>
      </div>
      <div class="menu-item">
        <van-icon name="service-o" size="30" />
        <span>呼叫服务</span>
      </div>
    </div>
    
    <!-- 推荐菜品 -->
    <div class="recommend">
      <h3>推荐菜品</h3>
      <div class="dish-list" v-if="recommendDishes.length > 0">
        <div 
          class="dish-item" 
          v-for="dish in recommendDishes" 
          :key="dish.id"
        >
          <img :src="dish.image || '/src/assets/dish-placeholder.jpg'" :alt="dish.name" />
          <div class="dish-info">
            <h4>{{ dish.name }}</h4>
            <p class="description">{{ dish.description }}</p>
            <div class="dish-footer">
              <span class="price">¥{{ dish.price }}</span>
              <van-button 
                type="primary" 
                size="small" 
                @click="addToCart(dish)"
              >
                加入购物车
              </van-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores/cart'
import { dishApi } from '@/api'
import { showToast } from 'vant'

const router = useRouter()
const cartStore = useCartStore()
const recommendDishes = ref([])

// 获取推荐菜品
const getRecommendDishes = async () => {
  try {
    const dishes = await dishApi.getDishList()
    recommendDishes.value = dishes.slice(0, 4) // 取前4个作为推荐
  } catch (error) {
    console.error('获取推荐菜品失败:', error)
  }
}

// 页面跳转
const goToMenu = () => {
  router.push('/menu')
}

const goToCart = () => {
  router.push('/cart')
}

const goToOrder = () => {
  router.push('/order')
}

// 添加到购物车
const addToCart = (dish) => {
  cartStore.addToCart(dish)
  showToast('已添加到购物车')
}

onMounted(() => {
  getRecommendDishes()
})
</script>

<style scoped>
.home {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.swipe {
  height: 200px;
}

.swipe-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.banner-content h2 {
  font-size: 24px;
  margin-bottom: 8px;
}

.banner-content p {
  font-size: 14px;
  opacity: 0.8;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  padding: 20px;
  gap: 15px;
  background: white;
  margin: 10px;
  border-radius: 10px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  position: relative;
  cursor: pointer;
}

.menu-item span {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.menu-item .van-badge {
  position: absolute;
  top: 10px;
  right: 10px;
}

.recommend {
  padding: 0 10px;
}

.recommend h3 {
  padding: 15px 0;
  color: #333;
  font-size: 16px;
}

.dish-list {
  display: grid;
  gap: 10px;
}

.dish-item {
  display: flex;
  background: white;
  border-radius: 10px;
  padding: 15px;
  align-items: center;
}

.dish-item img {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 15px;
}

.dish-info {
  flex: 1;
}

.dish-info h4 {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.dish-info .description {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
  line-height: 1.4;
}

.dish-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price {
  color: #ff6b35;
  font-size: 16px;
  font-weight: bold;
}
</style>