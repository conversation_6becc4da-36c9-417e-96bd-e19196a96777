# 扫码点餐系统

## 项目简介
基于Vue + Spring Boot + MySQL的扫码点餐小程序

## 技术栈
- 前端：Vue 3 + Vant + Vite
- 后端：Spring Boot + MyBatis Plus + MySQL
- 支付：微信支付 + 支付宝支付

## 项目结构
```
qr-ordering-system/
├── backend/          # Spring Boot后端
├── frontend/         # Vue前端客户端
├── admin/           # Vue管理后台（待开发）
└── database/        # 数据库脚本
```

## 功能模块
1. 客户端：浏览菜单、购物车、下单、支付
2. 管理后台：菜品管理、订单管理、统计分析（待开发）

## 环境要求
- Java 8+
- Node.js 16+
- MySQL 8.0+
- Maven 3.6+

## 快速开始

### 1. 数据库准备
```bash
# 在MySQL中执行数据库初始化脚本
mysql -u root -p < database/init.sql
```

### 2. 后端启动
```bash
cd backend
# 修改 application.yml 中的数据库连接信息
# 确保MySQL服务已启动
mvn spring-boot:run
```
后端服务将在 http://localhost:8080 启动

### 3. 前端启动
```bash
cd frontend
# 安装依赖
npm install
# 启动开发服务器
npm run dev
```
前端服务将在 http://localhost:3000 启动

### 4. 访问应用
- 客户端：http://localhost:3000
- API文档：http://localhost:8080/doc.html

## 开发说明

### 后端开发
- 使用Spring Boot 2.7.14
- 集成MyBatis Plus进行数据库操作
- 使用Knife4j生成API文档
- 支持跨域访问

### 前端开发
- 使用Vue 3 + Composition API
- 使用Vant UI组件库（移动端优化）
- 使用Pinia进行状态管理
- 使用Vite构建工具

### 数据库设计
- 分类表（category）：菜品分类管理
- 菜品表（dish）：菜品信息管理  
- 餐桌表（dining_table）：餐桌和二维码管理
- 订单表（orders）：订单主表
- 订单详情表（order_detail）：订单商品详情
- 管理员表（admin）：后台管理员账号

## 待开发功能
- [ ] 管理后台界面
- [ ] 订单管理系统
- [ ] 支付功能集成
- [ ] 二维码生成
- [ ] 实时订单状态推送
- [ ] 数据统计分析